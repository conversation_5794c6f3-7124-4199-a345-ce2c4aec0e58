#!/usr/bin/env python3
"""
Debug script to test the commits endpoint issue.
"""

import asyncio
import urllib.parse
from nuggit.util.async_db import get_repository

async def test_commits_endpoint():
    """Test the commits endpoint logic step by step."""
    
    # Test repository ID
    repo_id = "StratosphereIPS/StratoCyberLab"
    print(f"Original repo_id: {repo_id}")
    
    # URL-decode the repository ID (as done in the commits endpoint)
    decoded_repo_id = urllib.parse.unquote(repo_id)
    print(f"Decoded repo_id: {decoded_repo_id}")
    
    # Test if repository exists with decoded ID
    repo = await get_repository(decoded_repo_id)
    if repo:
        print("✅ Repository found with decoded ID!")
        print(f"   Name: {repo['name']}")
        print(f"   ID: {repo['id']}")
    else:
        print("❌ Repository NOT found with decoded ID")
    
    # Test with encoded ID
    encoded_repo_id = urllib.parse.quote(repo_id, safe='')
    print(f"Encoded repo_id: {encoded_repo_id}")
    
    decoded_encoded_repo_id = urllib.parse.unquote(encoded_repo_id)
    print(f"Decoded encoded repo_id: {decoded_encoded_repo_id}")
    
    repo2 = await get_repository(decoded_encoded_repo_id)
    if repo2:
        print("✅ Repository found with decoded encoded ID!")
        print(f"   Name: {repo2['name']}")
        print(f"   ID: {repo2['id']}")
    else:
        print("❌ Repository NOT found with decoded encoded ID")

if __name__ == "__main__":
    asyncio.run(test_commits_endpoint())
